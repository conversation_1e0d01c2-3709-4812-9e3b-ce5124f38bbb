<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us - ImgNinja</title>
  <link rel="stylesheet" href="../css/styles.css">
  <link rel="stylesheet" href="../css/responsive.css">
  <!-- Add Poppins Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- Add Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Favicon -->
  <link rel="icon" href="../assets/favicon-imgNinja.png" type="image/png">
  <style>
    /* Additional CSS Variables */
    :root {
      --success-color: #4CAF50;
      --danger-color: #F44336;
    }

    /* Contact Page Styles */
    .container {
      width: 80%;
      max-width: 80%;
    }

    /* Hero Section Enhancement */
    .hero-section {
      position: relative;
      overflow: hidden;
      padding: 40px 20px;
      margin-bottom: 60px;
    }

    .hero-section h1 {
      position: relative;
      z-index: 2;
      font-size: 3rem;
      margin-bottom: 20px;
      background: linear-gradient(to right, var(--text-color), var(--highlight-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 30%, var(--card-highlight) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, var(--card-highlight) 0%, transparent 40%);
      opacity: 0.5;
      z-index: 0;
    }

    .subtitle {
      font-size: 1.2rem;
      color: var(--accent-light);
      max-width: 600px;
      margin: 0 auto 30px;
      position: relative;
      z-index: 2;
    }

    /* Main Content Container */
    .page-content {
      width: 100%;
      margin: 0 auto;
      padding: 40px;
      text-align: left;
      background-color: var(--secondary-color);
      background-image: var(--card-gradient);
      border-radius: 15px;
      box-shadow: var(--card-shadow);
      margin-bottom: 40px;
      position: relative;
      overflow: hidden;
      border: 1px solid var(--border-color);
    }

    .page-content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 10% 90%, var(--card-highlight) 0%, transparent 40%),
        radial-gradient(circle at 90% 10%, var(--card-highlight) 0%, transparent 40%);
      opacity: 0.5;
      z-index: 0;
      pointer-events: none;
    }

    .page-content h2 {
      color: var(--highlight-color);
      margin-top: 30px;
      margin-bottom: 20px;
      font-size: 1.8rem;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .page-content h2::after {
      content: '';
      flex: 1;
      height: 1px;
      background: linear-gradient(to right, var(--highlight-color), transparent);
      margin-left: 15px;
    }

    .page-content p {
      margin-bottom: 20px;
      line-height: 1.8;
      font-size: 1.05rem;
      color: var(--accent-light);
      position: relative;
      z-index: 1;
    }

    /* Contact Form Styles */
    .contact-form-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      margin-top: 40px;
      position: relative;
      z-index: 1;
    }

    .contact-form {
      grid-column: 1 / 2;
      position: relative;
    }

    .form-group {
      margin-bottom: 25px;
      position: relative;
    }

    .form-group label {
      display: block;
      margin-bottom: 10px;
      font-weight: 500;
      color: var(--text-color);
      font-size: 1.05rem;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-group label i {
      color: var(--highlight-color);
      font-size: 1.1rem;
    }

    .form-control {
      width: 100%;
      padding: 15px 18px;
      background-color: rgba(20, 20, 20, 0.7);
      border: 1px solid var(--accent-color);
      border-radius: 8px;
      color: var(--text-color);
      font-family: 'Poppins', sans-serif;
      font-size: 1rem;
      transition: all 0.3s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) inset;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--highlight-color);
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }

    .form-control::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }

    textarea.form-control {
      min-height: 180px;
      resize: none;
    }

    .submit-btn {
      background-color: var(--accent-color);
      color: var(--text-color);
      border: none;
      padding: 15px 30px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
      position: relative;
      overflow: hidden;
    }

    .submit-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: all 0.6s ease;
    }

    .submit-btn:hover {
      background-color: var(--highlight-color);
      transform: translateY(-3px);
      color: black;
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    }

    .submit-btn:hover::before {
      left: 100%;
    }

    .submit-btn:active {
      transform: translateY(-1px);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    }

    /* Form validation styles */
    .form-group.success .form-control {
      border-color: var(--success-color);
    }

    .form-group.error .form-control {
      border-color: var(--danger-color);
    }

    .form-message {
      font-size: 0.85rem;
      margin-top: 5px;
      display: none;
    }

    .form-group.error .form-message {
      display: block;
      color: var(--danger-color);
    }

    .form-group.success .form-message {
      display: block;
      color: var(--success-color);
    }

    /* Contact Info Cards */
    .contact-info {
      grid-column: 2 / 3;
      margin-top: 50px;
      display: flex;
      flex-direction: column;
      gap: 25px;
    }

    .contact-card {
      background-color: rgba(20, 20, 20, 0.7);
      padding: 25px;
      border-radius: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
      box-shadow: var(--card-shadow);
      position: relative;
      overflow: hidden;
    }

    .contact-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      background: linear-gradient(90deg, var(--accent-color), var(--highlight-color));
      opacity: 0.7;
    }

    .contact-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    }

    .contact-card i {
      font-size: 2.5rem;
      color: var(--highlight-color);
      margin-bottom: 20px;
      transition: all 0.3s ease;
    }

    .contact-card:hover i {
      transform: scale(1.1);
    }

    .contact-card h3 {
      font-size: 1.3rem;
      margin-bottom: 15px;
      color: var(--text-color);
    }

    .contact-card p {
      color: var(--accent-light);
      margin-bottom: 8px;
      font-size: 0.95rem;
    }

    .contact-card a {
      color: var(--highlight-color);
      text-decoration: none;
      transition: all 0.3s ease;
      display: inline-block;
      margin-top: 5px;
      font-weight: 500;
    }

    .contact-card a:hover {
      transform: translateX(5px);
    }

    /* Social Media Section */
    .social-media {
      margin-top: 40px;
      text-align: center;
      position: relative;
      z-index: 1;
    }

    .social-media h3 {
      font-size: 1.3rem;
      margin-bottom: 20px;
      color: var(--text-color);
    }

    .social-icons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 15px;
    }

    .social-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: var(--accent-color);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      color: var(--text-color);
      font-size: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .social-icon:hover {
      transform: translateY(-5px) rotate(10deg);
      background-color: var(--highlight-color);
      color: black;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    }

    /* Success Message */
    .success-message {
      display: none;
      background-color: rgba(144, 144, 144, 0.1);
      border-left: 4px solid var(--success-color);
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
      animation: fadeIn 0.5s ease-out;
    }

    .success-message.show {
      display: block;
    }

    .success-message h3 {
      color: var(--success-color);
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
      .contact-form-container {
        grid-template-columns: 1fr;
        gap: 30px;
      }

      .contact-form, .contact-info {
        grid-column: 1 / 2;
      }

      .contact-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
      }
    }

    @media (max-width: 768px) {
      .container {
        width: 90%;
        max-width: 90%;
      }

      .page-content {
        padding: 25px;
      }

      .hero-section h1 {
        font-size: 2.5rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    @media (max-width: 480px) {
      .container {
        width: 95%;
        max-width: 95%;
      }

      .page-content {
        padding: 20px 15px;
      }

      .hero-section h1 {
        font-size: 2rem;
      }

      .form-group label {
        font-size: 0.95rem;
      }

      .form-control {
        padding: 12px 15px;
      }

      .submit-btn {
        width: 100%;
        justify-content: center;
      }

      .social-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
      }

      .footer-logo span {
        font-size: 0.9rem;
      }
    }

    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .animate-in {
      animation: fadeIn 0.6s ease-out forwards;
    }

    .form-group {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeIn 0.5s ease-out forwards;
    }

    .form-group:nth-child(1) { animation-delay: 0.1s; }
    .form-group:nth-child(2) { animation-delay: 0.2s; }
    .form-group:nth-child(3) { animation-delay: 0.3s; }
    .form-group:nth-child(4) { animation-delay: 0.4s; }

    .contact-card {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeIn 0.5s ease-out forwards;
    }

    .contact-card:nth-child(1) { animation-delay: 0.3s; }
    .contact-card:nth-child(2) { animation-delay: 0.4s; }
  </style>
</head>
<body>
  <!-- Navigation Bar -->
  <nav>
    <div class="logo">
      <a href="../index.html">
        <img src="../assets/logo-imgNinja.png" alt="ImgNinja Logo" width="170" height="90">
      </a>
    </div>
    <div class="menu-toggle" id="mobile-menu">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
    <ul class="nav-links">
      <li><a href="../index.html"><i class="fas fa-home"></i> Home</a></li>
      <li><a href="about-us.html"><i class="fas fa-info-circle"></i> About</a></li>
      <li><a href="../blog/index.html"><i class="fas fa-blog"></i> Blog</a></li>
      <li><a href="../image-compress.html"><i class="fas fa-image"></i> Image Compressor</a></li>
    </ul>
  </nav>

  <!-- Main Content -->
  <div class="container">
    <div class="hero-section">
      <h1>Contact Us</h1>
      <p class="subtitle">We'd love to hear from you! Reach out with questions, feedback, or support needs.</p>
    </div>

    <div class="page-content">
      <!-- Success Message (Hidden by default) -->
      <div id="successMessage" class="success-message">
        <h3><i class="fas fa-check-circle"></i> Message Sent Successfully</h3>
        <p>Thank you for reaching out! We've received your message and will get back to you as soon as possible.</p>
      </div>

      <p>Whether you have a question about our image compression service, need technical support, or want to provide feedback, our team is here to help. Fill out the form below, and we'll respond promptly.</p>

      <div class="contact-form-container">
        <div class="contact-form">
          <h2><i class="fas fa-paper-plane"></i> Send Us a Message</h2>
          <form id="contactForm">
            <div class="form-group">
              <label for="name"><i class="fas fa-user"></i> Your Name</label>
              <input type="text" id="name" class="form-control" placeholder="Enter your full name" required>
              <span class="form-message"></span>
            </div>

            <div class="form-group">
              <label for="email"><i class="fas fa-envelope"></i> Email Address</label>
              <input type="email" id="email" class="form-control" placeholder="Enter your email address" required>
              <span class="form-message"></span>
            </div>

            <div class="form-group">
              <label for="subject"><i class="fas fa-tag"></i> Subject</label>
              <input type="text" id="subject" class="form-control" placeholder="What is your message about?" required>
              <span class="form-message"></span>
            </div>

            <div class="form-group">
              <label for="message"><i class="fas fa-comment-alt"></i> Message</label>
              <textarea id="message" class="form-control" placeholder="Type your message here..." required></textarea>
              <span class="form-message"></span>
            </div>

            <button type="submit" class="submit-btn">
              <i class="fas fa-paper-plane"></i> Send Message
            </button>
          </form>
        </div>

        <div class="contact-info">
          <div class="contact-card">
            <i class="fas fa-envelope"></i>
            <h3>Email Us</h3>
            <p>For general inquiries:</p>
            <a href="mailto:<EMAIL>"><EMAIL></a>
            <p>For support:</p>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>

          <div class="contact-card">
            <i class="fas fa-clock"></i>
            <h3>Response Time</h3>
            <p>We aim to respond to all inquiries within 24-48 hours during business days.</p>
            <p>For urgent matters, please include "URGENT" in your subject line.</p>
          </div>
        </div>
      </div>


    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <!-- Column 1: Logo and About -->
      <div class="footer-column">
        <a href="../index.html" class="footer-logo">
          <img src="../assets/logo-imgNinja.png" alt="ImgNinja Logo" width="200" height="110">
        </a>
        <p class="footer-description">
          ImgNinja provides powerful, free online tools for image compression, helping you optimize your images without sacrificing quality.
        </p>

      </div>

      <!-- Column 2: Quick Links -->
      <div class="footer-column">
        <h3 class="footer-heading">Quick Links</h3>
        <div class="footer-links">
          <a href="../index.html"><i class="fas fa-home"></i> Home</a>
          <a href="../blog/index.html"><i class="fas fa-blog"></i> Blog</a>
          <a href="about-us.html"><i class="fas fa-info-circle"></i> About Us</a>
          <a href="contact-us.html"><i class="fas fa-envelope"></i> Contact Us</a>
        </div>
      </div>

      <!-- Column 3: Legal -->
      <div class="footer-column">
        <h3 class="footer-heading">Legal</h3>
        <div class="footer-links">
          <a href="privacy-policy.html"><i class="fas fa-shield-alt"></i> Privacy Policy</a>
          <a href="terms-conditions.html"><i class="fas fa-file-contract"></i> Terms & Conditions</a>
          <a href="dmca.html"><i class="fas fa-copyright"></i> DMCA</a>
          <a href="privacy-policy.html"><i class="fas fa-cookie"></i> Cookie Policy</a>
        </div>
      </div>

      <!-- Column 4: Tools -->
      <div class="footer-column">
        <h3 class="footer-heading">Our Tools</h3>
        <div class="footer-links">
          <a href="../image-compress.html"><i class="fas fa-image"></i> Image Compressor</a>
          <a href="../image-compress.html"><i class="fas fa-crop-alt"></i> Image Resizer</a>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <p class="copyright">&copy; 2025 ImgNinja. All rights reserved. | Made by <a href="https://totalsolution.42web.io/" target="_blank">Total Solution</a></p>
      </div>
    </div>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" title="Back to Top"><i class="fas fa-arrow-up"></i></a>
  </footer>

  <!-- Back to Top Script -->
  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.querySelector('.back-to-top');

      // Show/hide button based on scroll position
      window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
          backToTopButton.classList.add('visible');
        } else {
          backToTopButton.classList.remove('visible');
        }
      });

      // Smooth scroll to top when clicked
      backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    });
  </script>

  <script>
    // Mobile menu toggle
    document.getElementById('mobile-menu').addEventListener('click', function() {
      this.classList.toggle('active');
      document.querySelector('.nav-links').classList.toggle('active');
      document.body.classList.toggle('menu-open');
    });

    // Form validation and submission handler
    const contactForm = document.getElementById('contactForm');
    const formInputs = contactForm.querySelectorAll('.form-control');
    const successMessage = document.getElementById('successMessage');

    // Validate form fields
    function validateField(field) {
      const formGroup = field.parentElement;
      const formMessage = formGroup.querySelector('.form-message');
      let isValid = true;

      // Remove existing validation classes
      formGroup.classList.remove('success', 'error');

      // Check if field is empty
      if (!field.value.trim()) {
        formGroup.classList.add('error');
        formMessage.textContent = 'This field is required';
        isValid = false;
      }
      // Email validation
      else if (field.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(field.value)) {
        formGroup.classList.add('error');
        formMessage.textContent = 'Please enter a valid email address';
        isValid = false;
      }
      // Name validation (at least 2 characters)
      else if (field.id === 'name' && field.value.trim().length < 2) {
        formGroup.classList.add('error');
        formMessage.textContent = 'Name must be at least 2 characters';
        isValid = false;
      }
      // Subject validation (at least 3 characters)
      else if (field.id === 'subject' && field.value.trim().length < 3) {
        formGroup.classList.add('error');
        formMessage.textContent = 'Subject must be at least 3 characters';
        isValid = false;
      }
      // Message validation (at least 10 characters)
      else if (field.id === 'message' && field.value.trim().length < 10) {
        formGroup.classList.add('error');
        formMessage.textContent = 'Message must be at least 10 characters';
        isValid = false;
      }
      else {
        formGroup.classList.add('success');
        formMessage.textContent = '';
      }

      return isValid;
    }

    // Add input event listeners for real-time validation
    formInputs.forEach(input => {
      input.addEventListener('input', function() {
        validateField(this);
      });

      input.addEventListener('blur', function() {
        validateField(this);
      });
    });

    // Form submission handler
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Validate all fields
      let isFormValid = true;
      formInputs.forEach(input => {
        if (!validateField(input)) {
          isFormValid = false;
        }
      });

      // If form is valid, submit it
      if (isFormValid) {
        // Show loading state on button
        const submitBtn = this.querySelector('.submit-btn');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        // Simulate form submission (replace with actual form submission)
        setTimeout(() => {
          // Reset form
          this.reset();

          // Reset validation classes
          formInputs.forEach(input => {
            input.parentElement.classList.remove('success', 'error');
          });

          // Show success message
          successMessage.classList.add('show');

          // Reset button
          submitBtn.innerHTML = originalBtnText;
          submitBtn.disabled = false;

          // Scroll to success message
          successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

          // Hide success message after 5 seconds
          setTimeout(() => {
            successMessage.classList.remove('show');
          }, 5000);
        }, 1500);
      } else {
        // Scroll to the first error
        const firstError = document.querySelector('.form-group.error');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    });

    // Add subtle parallax effect to hero section
    window.addEventListener('mousemove', function(e) {
      const heroSection = document.querySelector('.hero-section');
      const moveX = (e.clientX - window.innerWidth / 2) * 0.01;
      const moveY = (e.clientY - window.innerHeight / 2) * 0.01;

      heroSection.style.backgroundPosition = `${moveX}px ${moveY}px`;
    });
  </script>
</body>
</html>
